{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"client-portal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "apps/client-portal", "sourceRoot": "apps/client-portal/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/client-portal", "index": "apps/client-portal/src/index.html", "browser": "apps/client-portal/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/client-portal/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/client-portal/public"}], "styles": ["apps/client-portal/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "client-portal:build:production"}, "development": {"buildTarget": "client-portal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "apps/client-portal/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/client-portal/public"}], "styles": ["apps/client-portal/src/styles.scss"], "scripts": []}}}}}}