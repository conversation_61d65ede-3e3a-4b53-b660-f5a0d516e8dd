import { Routes } from '@angular/router';

export const AUTH_ROUTES: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.component').then((m) => m.LoginComponent),
  },
  {
    path: 'forgot-password',
    children: [
      {
        path: '',
        redirectTo: 'enter-email',
        pathMatch: 'full',
      },
      {
        path: 'enter-email',
        loadComponent: () =>
          import('./pages/forgot-password/enter-email/enter-email.component').then(
            (m) => m.EnterEmailComponent,
          ),
      },
      {
        path: 'check-email',
        loadComponent: () =>
          import('./pages/forgot-password/check-email/check-email.component').then(
            (m) => m.CheckEmailComponent,
          ),
      },
      {
        path: 'change-password',
        loadComponent: () =>
          import('./pages/forgot-password/change-password/change-password.component').then(
            (m) => m.ChangePasswordComponent,
          ),
      },
      {
        path: 'success',
        loadComponent: () =>
          import('./pages/forgot-password/change-success/change-success.component').then(
            (m) => m.ChangeSuccessComponent,
          ),
      },
    ],
  },
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
];
