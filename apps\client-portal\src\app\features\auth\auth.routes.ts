import { Routes } from '@angular/router';

export const AUTH_ROUTES: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.component').then((m) => m.LoginComponent),
  },
  //   {
  //     path: 'forgot-password',
  //     loadChildren: () => [
  //       {
  //         path: 'step1',
  //         loadComponent: () => import('./pages/forgot-password/step1-email/step1-email.component')
  //       },
  //       {
  //         path: 'step2',
  //         loadComponent: () => import('./pages/forgot-password/step2-otp/step2-otp.component')
  //       }
  //       // ... other steps
  //     ]
  //   }
];
