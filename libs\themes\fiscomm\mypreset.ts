import { definePreset } from '@primeng/themes';

import <PERSON> from './';

const MyPreset = definePreset(<PERSON>, {
  semantic: {
    primary: {
      50: '#C7F2ED',
      100: '#A2E0DA',
      200: '#7DCFC6',
      300: '#58BDB3',
      400: '#37A2A0',
      500: '#15878D',
      600: '#0B6676',
      700: '#00455F',
      surface: '#58BDB330',
    },
    secondary: {
      '700': '#657196',
      '600': '#7A85A5',
      '500': '#8E98B4',
      '400': '#A3ACC3',
      '300': '#B8C0D2',
      '200': '#CDD4E1',
      '100': '#E1E7F0',
      '50': '#F1F3F9',
      '30': 'rgba(184, 192, 210, 0.3)',
    },
    warning: {
      50: '#F2EBE5',
      100: '#F2DAC3',
      200: '#F1C9A0',
      300: '#DEA166',
      400: '#E19448',
      500: '#D07011',
      600: '#B8630F',
      700: '#A0570D',
      surface: 'rgba(222, 161, 102, 0.3)',
    },
  },
});

export default MyPreset;
