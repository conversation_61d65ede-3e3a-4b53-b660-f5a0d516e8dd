import type { DatePickerTokenSections } from '@primeuix/themes/types/datepicker';

export * from '@primeuix/themes/types/datepicker';

declare const root: DatePickerTokenSections.Root;
declare const panel: DatePickerTokenSections.Panel;
declare const header: DatePickerTokenSections.Header;
declare const title: DatePickerTokenSections.Title;
declare const dropdown: DatePickerTokenSections.Dropdown;
declare const inputIcon: DatePickerTokenSections.InputIcon;
declare const selectMonth: DatePickerTokenSections.SelectMonth;
declare const selectYear: DatePickerTokenSections.SelectYear;
declare const group: DatePickerTokenSections.Group;
declare const dayView: DatePickerTokenSections.DayView;
declare const weekDay: DatePickerTokenSections.WeekDay;
declare const date: DatePickerTokenSections.Date;
declare const monthView: DatePickerTokenSections.MonthView;
declare const month: DatePickerTokenSections.Month;
declare const yearView: DatePickerTokenSections.YearView;
declare const year: DatePickerTokenSections.Year;
declare const buttonbar: DatePickerTokenSections.Buttonbar;
declare const timePicker: DatePickerTokenSections.TimePicker;
declare const colorScheme: DatePickerTokenSections.ColorScheme;
declare const _default: {
    root: DatePickerTokenSections.Root;
    panel: DatePickerTokenSections.Panel;
    header: DatePickerTokenSections.Header;
    title: DatePickerTokenSections.Title;
    dropdown: DatePickerTokenSections.Dropdown;
    inputIcon: DatePickerTokenSections.InputIcon;
    selectMonth: DatePickerTokenSections.SelectMonth;
    selectYear: DatePickerTokenSections.SelectYear;
    group: DatePickerTokenSections.Group;
    dayView: DatePickerTokenSections.DayView;
    weekDay: DatePickerTokenSections.WeekDay;
    date: DatePickerTokenSections.Date;
    monthView: DatePickerTokenSections.MonthView;
    month: DatePickerTokenSections.Month;
    yearView: DatePickerTokenSections.YearView;
    year: DatePickerTokenSections.Year;
    buttonbar: DatePickerTokenSections.Buttonbar;
    timePicker: DatePickerTokenSections.TimePicker;
    colorScheme: DatePickerTokenSections.ColorScheme;
};

export { buttonbar, colorScheme, date, dayView, _default as default, dropdown, group, header, inputIcon, month, monthView, panel, root, selectMonth, selectYear, timePicker, title, weekDay, year, yearView };
